import * as THREE from 'three';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass';
import { FXAAShader } from 'three/examples/jsm/shaders/FXAAShader';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass';
import { SceneManager } from './SceneManager';
import { CameraController } from '../lib/CameraController';
import { useGlobalThreeStore } from '../store/globalThreeStore';

interface Stats {
  showPanel: (panel: number) => void;
  begin: () => void;
  end: () => void;
  update: () => void;
  dom: HTMLDivElement;
}

// 扩展 Mesh 接口以包含 boundingSphere 属性
interface ExtendedMesh extends THREE.Mesh {
  boundingSphere?: THREE.Sphere | null;
}

declare global {
  interface Window {
    Stats?: new () => Stats;
    animationLoop?: {
      getFps: () => number;
    };
  }
}

export class RenderingPipeline {
  private static instance: RenderingPipeline;
  private renderer: THREE.WebGLRenderer = new THREE.WebGLRenderer();
  private camera: THREE.Camera = new THREE.PerspectiveCamera();
  private scene: THREE.Scene = new THREE.Scene();
  private container: HTMLElement = document.createElement('div');
  private composer: EffectComposer = new EffectComposer(this.renderer);
  private fxaaPass: ShaderPass = new ShaderPass(FXAAShader);
  // 移除未使用的后处理通道
  private stats?: Stats;
  private idRenderTarget: THREE.WebGLRenderTarget | null = null;

  private culledObjects: Set<number> = new Set();
  private lastCullTime: number = 0;
  private cullInterval: number = 500;

  private static renderCounter: number = 0;
  private static lastRenderTime: number = 0;
  private static frameRateLimit: number = 60; // 默认帧率限制
  private static roamingMode: boolean = false; // 是否处于漫游模式

  // 新增: 配置类型定义
  private savedConfig: {
    toneMapping: THREE.ToneMapping;
    toneMappingExposure: number;
    shadows: boolean;
    shadowMapType: THREE.ShadowMapType;
    pixelRatio: number;
  } | null = null;

  constructor() {
    if (RenderingPipeline.instance) {
      return RenderingPipeline.instance;
    }

    const globalThreeStore = useGlobalThreeStore();
    const container = globalThreeStore.containerRef;
    if (!container) {
      throw new Error('Container is not defined');
    }
    this.container = container as HTMLElement;

    const camera = CameraController.getInstance().camera;
    const width = container.clientWidth;
    const height = container.clientHeight;
    const sceneManager = SceneManager.getInstance();

    // 先尝试加载保存的渲染配置
    this.loadSavedRenderConfig();

    this.renderer = new THREE.WebGLRenderer({
      antialias: true,
      powerPreference: 'high-performance',
      stencil: false,
      alpha: true,
      premultipliedAlpha: false,
      precision: 'highp', // 提高精度
      logarithmicDepthBuffer: true, // 启用对数深度缓冲
    });

    // 应用保存的渲染配置
    if (this.savedConfig) {
      this.renderer.toneMapping = this.savedConfig.toneMapping;
      this.renderer.toneMappingExposure = this.savedConfig.toneMappingExposure;
      this.renderer.shadowMap.enabled = this.savedConfig.shadows;
      this.renderer.shadowMap.type = this.savedConfig.shadowMapType;
      this.renderer.setPixelRatio(this.savedConfig.pixelRatio);
    } else {
      // 使用默认配置
      this.renderer.setPixelRatio(window.devicePixelRatio);
      this.renderer.outputColorSpace = THREE.SRGBColorSpace;
      this.renderer.shadowMap.enabled = false;
    }

    this.renderer.setSize(width, height);
    this.renderer.setClearColor(0x000000, 0);

    container.appendChild(this.renderer.domElement);

    this.camera = camera;
    this.scene = sceneManager.scene;

    if (process.env.NODE_ENV === 'development') {
      this._setupPerformanceMonitoring();
    }

    RenderingPipeline.instance = this;
  }

  // 新增: 加载保存的渲染配置
  private loadSavedRenderConfig(): void {
    try {
      // 获取当前场景类型的配置
      const sceneType = SceneManager.getInstance().getCurrentSceneType();
      const configKey = `debug_gui_config_${sceneType}`;
      const savedConfig = localStorage.getItem(configKey);

      if (savedConfig) {
        const config = JSON.parse(savedConfig);
        // 只提取渲染相关的配置
        this.savedConfig = {
          toneMapping: config.rendering.toneMapping,
          toneMappingExposure: config.rendering.toneMappingExposure,
          shadows: config.rendering.shadows,
          shadowMapType: config.rendering.shadowMapType,
          pixelRatio: config.rendering.pixelRatio,
        };
        console.log(`[RenderingPipeline] 已加载${sceneType}场景渲染配置`);
      }
    } catch (error) {
      console.warn('[RenderingPipeline] 加载渲染配置失败:', error);
      this.savedConfig = null;
    }
  }

  // 简化版本，移除后处理
  private _initPostprocessing(camera: THREE.Camera, scene: THREE.Scene): void {
    const width = this.renderer.domElement.width;
    const height = this.renderer.domElement.height;

    // 初始化ID缓冲区用于GPU Picking
    this.idRenderTarget = new THREE.WebGLRenderTarget(width, height, {
      minFilter: THREE.NearestFilter,
      magFilter: THREE.NearestFilter,
      format: THREE.RGBAFormat,
      stencilBuffer: false,
    });
  }

  public static getInstance(): RenderingPipeline {
    if (!RenderingPipeline.instance) {
      RenderingPipeline.instance = new RenderingPipeline();
    }
    return RenderingPipeline.instance;
  }

  public resize(): void {
    const globalThreeStore = useGlobalThreeStore();
    const container = globalThreeStore.containerRef;

    // 获取实际的容器尺寸，考虑PPT演示模式
    let newWidth = container?.clientWidth || 0;
    let newHeight = container?.clientHeight || 0;

    // 如果容器尺寸为0，使用备用计算方法
    if (newWidth === 0 || newHeight === 0) {
      if (globalThreeStore.pptDemonstration.isActive) {
        // PPT演示模式下，3D场景只占屏幕的一半宽度
        newWidth = window.innerWidth / 2;
        newHeight = window.innerHeight;
      } else {
        // 默认使用全屏尺寸
        newWidth = window.innerWidth;
        newHeight = window.innerHeight;
      }
    }

    this.renderer.setSize(newWidth, newHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);

    if (this.composer) {
      this.composer.setSize(newWidth, newHeight);
      if (this.fxaaPass) {
        // 更新 FXAA 分辨率，考虑像素比例
        this.fxaaPass.uniforms['resolution'].value.set(
          1 / (newWidth * 1.5 * window.devicePixelRatio),
          1 / (newHeight * 1.5 * window.devicePixelRatio)
        );
      }
    }

    if (this.idRenderTarget) {
      this.idRenderTarget.setSize(newWidth, newHeight);
    }
  }

  /**
   * 设置帧率限制
   * @param fps 目标帧率
   */
  public setFrameRateLimit(fps: number): void {
    RenderingPipeline.frameRateLimit = Math.max(30, Math.min(120, fps));
    console.log(`[RenderingPipeline] 帧率限制设置为: ${RenderingPipeline.frameRateLimit} FPS`);
  }

  /**
   * 设置漫游模式
   * @param enabled 是否启用漫游模式
   */
  public setRoamingMode(enabled: boolean): void {
    RenderingPipeline.roamingMode = enabled;

    // 在漫游模式下优化渲染设置
    if (enabled) {
      // 提高帧率限制
      this.setFrameRateLimit(120);

      // 优化渲染器设置
      if (this.renderer) {
        // 暂时降低阴影质量以提高性能
        this.renderer.shadowMap.type = THREE.BasicShadowMap;
      }

      console.log('[RenderingPipeline] 已为漫游模式优化渲染设置');
    } else {
      // 恢复默认设置
      this.setFrameRateLimit(60);

      if (this.renderer) {
        // 恢复阴影质量
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
      }

      console.log('[RenderingPipeline] 已恢复默认渲染设置');
    }
  }

  /**
   * 渲染场景 - 简化版本
   * 保留帧率限制但移除复杂后处理
   */
  public render(): void {
    // 帧率限制
    const now = performance.now();
    const frameInterval = 1000 / RenderingPipeline.frameRateLimit;
    const elapsed = now - RenderingPipeline.lastRenderTime;

    // 漫游模式下始终渲染，确保流畅
    if (!RenderingPipeline.roamingMode) {
      // 如果距离上次渲染时间不足一帧间隔，跳过本次渲染
      if (elapsed < frameInterval) {
        return;
      }
    }

    // 更新上次渲染时间
    RenderingPipeline.lastRenderTime = now - (elapsed % frameInterval);

    // 增加渲染计数器
    RenderingPipeline.renderCounter++;

    // 极度性能优化：只在每3帧更新一次视锥体剔除
    const cullInterval = RenderingPipeline.roamingMode ? 5 : 3;
    if (RenderingPipeline.renderCounter % cullInterval === 0) {
      this._updateFrustumCulling();
    }

    // 简化的渲染流程 - 直接使用标准渲染
    this.renderer.render(this.scene, this.camera);

    // 确保渲染目标被重置
    this.renderer.setRenderTarget(null);

    // 性能监控更新
    if (this.stats) {
      this.stats.update();
    }
  }

  // 静态临时对象，避免每次创建新对象
  private static tempFrustum = new THREE.Frustum();
  private static tempMatrix4 = new THREE.Matrix4();
  private static tempVector3 = new THREE.Vector3();
  private static tempQuaternion = new THREE.Quaternion();
  private static tempScale = new THREE.Vector3();
  private static tempSphere = new THREE.Sphere();

  /**
   * 更新视锥体剔除 - 极度性能优化版本
   * 使用静态临时对象和批处理技术
   */
  private _updateFrustumCulling(): void {
    const now = performance.now();
    if (now - this.lastCullTime < this.cullInterval) return;
    this.lastCullTime = now;

    // 使用静态临时对象
    const frustum = RenderingPipeline.tempFrustum;
    const projScreenMatrix = RenderingPipeline.tempMatrix4;

    // 避免克隆矩阵，直接使用原始矩阵
    projScreenMatrix.multiplyMatrices(this.camera.projectionMatrix, this.camera.matrixWorldInverse);
    frustum.setFromProjectionMatrix(projScreenMatrix);

    // 获取可交互对象缓存
    const sceneManager = SceneManager.getInstance();
    const objectsToCheck = sceneManager.getInteractableObjects() || [];

    // 极度性能优化：批量处理对象
    // 每次最多处理100个对象，避免长时间阻塞主线程
    const maxObjectsPerFrame = 100;
    const objectsToProcess = Math.min(objectsToCheck.length, maxObjectsPerFrame);

    // 使用静态临时变量
    const tempScale = RenderingPipeline.tempScale;
    const tempPosition = RenderingPipeline.tempVector3;
    const tempQuaternion = RenderingPipeline.tempQuaternion;
    const tempSphere = RenderingPipeline.tempSphere;

    // 处理部分对象
    for (let i = 0; i < objectsToProcess; i++) {
      const object = objectsToCheck[i];

      // 只处理可剔除的网格对象
      if (!(object instanceof THREE.Mesh) || !object.userData.canCull) {
        continue;
      }

      const mesh = object as ExtendedMesh;

      // 跳过特定对象类型
      if (object.parent instanceof THREE.LOD || !object.visible) {
        continue;
      }

      // 确保有包围球
      if (!mesh.geometry.boundingSphere) {
        continue;
      }

      // 使用临时球体，避免克隆
      tempSphere.copy(mesh.geometry.boundingSphere);

      // 更新世界矩阵
      mesh.updateWorldMatrix(true, false);

      // 分解矩阵，使用临时变量
      mesh.matrixWorld.decompose(tempPosition, tempQuaternion, tempScale);

      // 考虑缩放影响包围球半径 - 使用简化计算
      const maxScale = Math.max(tempScale.x, Math.max(tempScale.y, tempScale.z));
      tempSphere.radius *= maxScale;

      // 设置球体中心点
      tempSphere.center.set(0, 0, 0).applyMatrix4(mesh.matrixWorld);

      // 检查对象是否在视锥体内 - 使用简化检测
      const isInFrustum = frustum.intersectsSphere(tempSphere);

      // 更新可见性状态 - 使用位运算优化条件检查
      const isCurrentlyCulled = this.culledObjects.has(mesh.id);

      if (!isInFrustum && !isCurrentlyCulled) {
        // 不在视锥体内且未被剔除，添加到剔除集合
        this.culledObjects.add(mesh.id);
        mesh.visible = false;
      } else if (isInFrustum && isCurrentlyCulled) {
        // 在视锥体内但被剔除，从剔除集合中移除
        this.culledObjects.delete(mesh.id);
        mesh.visible = true;
      }
    }
  }

  private _setupPerformanceMonitoring(): void {
    if (process.env.NODE_ENV === 'development') {
      const StatsConstructor = window.Stats;
      if (StatsConstructor) {
        this.stats = new StatsConstructor();
        this.stats.showPanel(0);
        document.body.appendChild(this.stats.dom);
      }
    }
  }

  public dispose(): void {
    if (this.composer) {
      this.composer.passes.forEach((pass) => {
        if (pass.dispose) {
          pass.dispose();
        }
      });
      this.composer = undefined!;
    }

    if (this.fxaaPass) {
      this.fxaaPass.dispose();
      this.fxaaPass = undefined!;
    }

    // 移除未使用的后处理通道代码

    if (this.idRenderTarget) {
      this.idRenderTarget.dispose();
      this.idRenderTarget = null;
    }

    if (this.renderer) {
      this.renderer.dispose();
      this.renderer.forceContextLoss();
      const gl = this.renderer.domElement.getContext('webgl');
      if (gl) {
        gl.getExtension('WEBGL_lose_context')?.loseContext();
      }
      this.renderer = undefined!;
    }

    if (this.container && this.renderer && this.renderer.domElement) {
      this.container.removeChild(this.renderer.domElement);
    }

    this.camera = undefined!;
    this.scene = undefined!;
    this.container = undefined!;

    RenderingPipeline.instance = undefined!;
  }

  /**
   * 设置动态模糊效果强度
   * @param intensity 模糊强度 (0-1.5)
   */
  public setMotionBlur(intensity: number): void {
    // 目前未实现动态模糊效果，此方法为占位符
    // 未来可以在此处添加动态模糊后处理效果
    console.log(`[RenderingPipeline] 动态模糊强度设置为: ${intensity}`);
  }

  /**
   * 获取渲染器实例
   * @returns THREE.WebGLRenderer 渲染器实例
   */
  public getRenderer(): THREE.WebGLRenderer {
    return this.renderer;
  }

  /**
   * 获取渲染器DOM元素
   * @returns HTMLCanvasElement 渲染器DOM元素
   */
  public getDomElement(): HTMLCanvasElement {
    return this.renderer.domElement;
  }

  // 添加一个公共方法获取ID渲染目标
  public getIdRenderTarget(): THREE.WebGLRenderTarget | null {
    return this.idRenderTarget;
  }

  // 添加一个方法来渲染ID缓冲区
  public renderIdBuffer(scene: THREE.Scene, camera: THREE.Camera, idMaterial: THREE.Material): void {
    if (this.idRenderTarget) {
      this.renderer.setRenderTarget(this.idRenderTarget);
      scene.overrideMaterial = idMaterial;
      this.renderer.render(scene, camera);
      scene.overrideMaterial = null;
      this.renderer.setRenderTarget(null);
    }
  }

  public optimizeRenderer(fps: number = 60): void {
    if (this.renderer) {
      // 根据帧率动态调整阴影质量
      if (fps < 30) {
        this.renderer.shadowMap.type = THREE.BasicShadowMap;
      } else if (fps < 45) {
        this.renderer.shadowMap.type = THREE.PCFShadowMap;
      } else {
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
      }

      // 根据帧率动态调整像素比例
      const pixelRatio =
        fps < 30 ? Math.min(window.devicePixelRatio, 1.0) : fps < 45 ? Math.min(window.devicePixelRatio, 1.5) : window.devicePixelRatio;
      this.renderer.setPixelRatio(pixelRatio);
    }
  }

  /**
   * 设置渲染器的色调映射模式 - 简化版本
   * @param toneMapping 色调映射模式
   * @param exposure 曝光度
   * @param isExterior 是否为外部场景
   */
  public setToneMapping(toneMapping: THREE.ToneMapping, exposure: number = 1.0, isExterior: boolean = false): void {
    // 简化版本不使用色调映射，以提高性能
    console.log('[RenderingPipeline] 色调映射已禁用以提高性能');
  }

  /**
   * 获取当前FPS
   * @returns 当前FPS
   */
  public getCurrentFps(): number {
    // 尝试从AnimationLoop获取FPS
    try {
      const animationLoop = window.animationLoop;
      if (animationLoop && typeof animationLoop.getFps === 'function') {
        return animationLoop.getFps();
      }
    } catch (error) {
      console.warn('[RenderingPipeline] 无法从AnimationLoop获取FPS:', error);
    }

    // 如果无法从AnimationLoop获取，则计算估计值
    const now = performance.now();
    const elapsed = now - RenderingPipeline.lastRenderTime;
    if (elapsed === 0) return 60; // 默认值

    // 基于帧间隔计算估计FPS
    const estimatedFps = Math.min(120, Math.round(1000 / elapsed));
    return estimatedFps > 0 ? estimatedFps : 60; // 确保返回合理值
  }
}
