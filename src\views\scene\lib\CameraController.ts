import * as THREE from 'three';
import { useGlobalThreeStore } from '../store/globalThreeStore';
import { SceneManager } from './SceneManager';

interface Position {
  x: number;
  y: number;
  z: number;
}

export class CameraController {
  private static instance: CameraController | null = null;
  public camera!: THREE.PerspectiveCamera;
  private defaultTarget!: THREE.Vector3;
  public currentTarget!: THREE.Vector3;
  private width: number = 0;
  private height: number = 0;

  constructor() {
    if (CameraController.instance) {
      return CameraController.instance;
    }

    const globalThreeStore = useGlobalThreeStore();
    const container = globalThreeStore.containerRef;
    if (!container) {
      throw new Error('Container ref is not available in globalThreeStore.');
    }
    this.width = container.clientWidth;
    this.height = container.clientHeight;
    this.camera = new THREE.PerspectiveCamera(75, this.width / this.height, 0.1, 1000);

    // 初始相机位置
    const cameraPosition: Position = {
      x: -45.45,
      y: 45.2,
      z: -70.2,
    };
    this.camera.position.set(cameraPosition.x, cameraPosition.y, cameraPosition.z);

    // 设置相机朝向
    const targetPoint = new THREE.Vector3(0, 10, 0);
    this.camera.lookAt(targetPoint);

    this.defaultTarget = targetPoint.clone();
    this.currentTarget = targetPoint.clone();

    CameraController.instance = this;
  }

  static getInstance(): CameraController {
    if (!CameraController.instance) {
      CameraController.instance = new CameraController();
    }
    return CameraController.instance!;
  }

  updateAspect(width?: number, height?: number): void {
    if (width && height) {
      this.width = width;
      this.height = height;
    } else {
      // 如果没有提供尺寸，获取实际的容器尺寸
      const actualSize = this.getActualContainerSize();
      this.width = actualSize.width;
      this.height = actualSize.height;
    }
    this.camera.aspect = this.width / this.height;
    this.camera.updateProjectionMatrix();

    // 强制渲染
    this.forceRender();
  }

  // 更新容器尺寸
  updateContainerSize(width: number, height: number): void {
    this.width = width;
    this.height = height;
    this.updateAspect();
  }

  /**
   * 获取实际的容器尺寸，考虑PPT演示模式等特殊情况
   */
  private getActualContainerSize(): { width: number; height: number } {
    const globalThreeStore = useGlobalThreeStore();
    const container = globalThreeStore.containerRef;

    // 如果容器存在且有实际尺寸，优先使用容器尺寸
    if (container && container.clientWidth > 0 && container.clientHeight > 0) {
      return {
        width: container.clientWidth,
        height: container.clientHeight,
      };
    }

    // 检查是否在PPT演示模式
    if (globalThreeStore.pptDemonstration.isActive) {
      // PPT演示模式下，3D场景只占屏幕的一半宽度
      return {
        width: window.innerWidth / 2,
        height: window.innerHeight,
      };
    }

    // 默认使用全屏尺寸
    return {
      width: window.innerWidth,
      height: window.innerHeight,
    };
  }

  getCameraPosition(): THREE.Vector3 {
    const position = new THREE.Vector3();
    this.camera.getWorldPosition(position);
    return position;
  }

  forceRender(): boolean {
    const sceneManager = SceneManager.getInstance();
    if (sceneManager && sceneManager.renderer) {
      sceneManager.needsRender = true;
      // 请求多个渲染帧确保更新
      for (let i = 0; i < 3; i++) {
        setTimeout(() => {
          if (sceneManager) {
            sceneManager.needsRender = true;
            sceneManager.render();
          }
        }, i * 100);
      }
      return true;
    }
    return false;
  }

  moveToPosition(position: Position, target: Position, duration: number = 1000, callback?: () => void): void {
    const startPosition = this.camera.position.clone();
    const startTarget = this.currentTarget.clone();
    const endPosition = new THREE.Vector3(position.x, position.y, position.z);
    const endTarget = new THREE.Vector3(target.x, target.y, target.z);

    const startTime = performance.now();

    const animate = (): void => {
      const currentTime = performance.now();
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      const easeProgress = this.easeInOutCubic(progress);

      this.camera.position.lerpVectors(startPosition, endPosition, easeProgress);

      const currentTarget = new THREE.Vector3();
      currentTarget.lerpVectors(startTarget, endTarget, easeProgress);
      this.camera.lookAt(currentTarget);

      const sceneManager = SceneManager.getInstance();
      if (sceneManager) {
        sceneManager.render();
      }

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        this.currentTarget.copy(endTarget);
        if (callback) callback();
      }
    };

    animate();
  }

  private easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  getDefaultTarget(): THREE.Vector3 {
    return this.defaultTarget;
  }
}
